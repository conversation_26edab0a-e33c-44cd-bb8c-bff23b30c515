# 音频缓存存储限制解决方案

## 问题描述
微信小程序在保存TTS音频文件时出现错误：
```
Error: fs_appendFileSync:fail the maximum size of the file storage limit is exceeded
```

## 问题原因
1. **存储空间限制**：微信小程序的 `USER_DATA_PATH` 存储空间有限制（通常10MB左右）
2. **文件累积**：每次TTS都会创建新的音频文件，旧文件清理不及时
3. **音频文件较大**：WAV格式的音频文件相对较大，容易超出限制

## 解决方案

### 1. 智能错误处理和重试机制
- 在 `fs.appendFileSync` 失败时检测是否为存储限制错误
- 自动清理旧文件后重试
- 如果重试仍失败，切换到内存模式

### 2. 主动存储空间管理
- 组件初始化时清理所有旧的音频文件
- 在开始新的TTS之前检查存储使用率
- 当存储使用率超过80%时主动清理

### 3. 改进的文件清理机制
- 增强 `cleanupSaveFile` 方法，即使删除失败也继续处理队列
- 新增 `cleanupOldAudioFiles` 方法，批量清理所有旧音频文件
- 跳过当前正在使用的文件，避免播放中断

### 4. 存储空间监控
- 新增 `checkAndCleanStorage` 方法监控存储使用情况
- 使用 `wx.getStorageInfoSync()` 获取存储信息
- 根据使用率决定是否需要清理

## 代码改进要点

### 错误处理改进
```javascript
// 在TTS数据接收时的错误处理
catch (e) {
  console.error('File append error:', e)
  if (e.errMsg && e.errMsg.includes('storage limit')) {
    this.cleanupOldAudioFiles().then(() => {
      // 重试逻辑
    })
  }
}
```

### 主动清理机制
```javascript
// 组件初始化时清理
async attached() {
  await this.cleanupOldAudioFiles()
  // 其他初始化逻辑...
}

// TTS开始前检查
async processTtsMessage(ttsMessage) {
  await this.checkAndCleanStorage()
  // TTS处理逻辑...
}
```

### 批量文件清理
```javascript
cleanupOldAudioFiles() {
  return new Promise((resolve) => {
    fs.readdir({
      dirPath: wx.env.USER_DATA_PATH,
      success: (res) => {
        const audioFiles = res.files.filter(file => file.endsWith('.wav'))
        // 批量删除逻辑...
      }
    })
  })
}
```

## 使用建议

### 1. 立即生效的改进
- 组件初始化时会自动清理旧文件
- 存储空间不足时会自动重试
- 删除失败不会阻塞TTS队列处理

### 2. 长期优化建议
- 考虑使用更高效的音频格式（如MP3）
- 实现音频文件的LRU缓存策略
- 添加用户设置来控制缓存大小

### 3. 监控和调试
- 查看控制台日志了解存储使用情况
- 监控文件清理的执行情况
- 注意观察重试机制的效果

## 预期效果
1. **减少存储错误**：主动清理机制大幅减少存储空间不足的情况
2. **提高稳定性**：错误重试机制提高TTS功能的可靠性
3. **更好的用户体验**：即使出现存储问题也不会中断TTS队列处理
4. **自动维护**：无需手动干预，系统自动管理存储空间

## 注意事项
- 清理操作是异步的，不会阻塞主要功能
- 保护当前正在播放的音频文件不被误删
- 所有改动都向后兼容，不影响现有功能
